import 'dart:async';
import 'dart:convert';
import 'dart:math'; // Import math for min function
import 'package:get/get.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/controllers/knowledge_base_controller.dart';
import 'package:novel_app/models/writing_style_package.dart';
import 'package:novel_app/models/character_card.dart';
import 'package:novel_app/models/novel_outline.dart';
import 'package:novel_app/langchain/chains/novel_generation_chain.dart';
import 'package:novel_app/langchain/utils/model_adapter.dart';
import 'package:novel_app/langchain/chains/detailed_outline_chain.dart';
import 'package:novel_app/langchain/models/novel_memory.dart'; // Import NovelMemory
import 'package:novel_app/langchain/prompts/novel_prompt_templates_enhanced.dart';
import 'package:novel_app/services/chat_history_service.dart';
import 'package:novel_app/models/chat_message.dart' as app_chat;
import 'package:novel_app/services/embedding_service.dart';
import 'package:langchain/langchain.dart';
import 'package:langchain_openai/langchain_openai.dart';

/// 使用LangChain实现的小说生成服务
class NovelGenerationService extends GetxService {
  final ApiConfigController _apiConfigController;
  late final EmbeddingService _embeddingService;

  // 缓存生成链，避免重复创建
  final Map<String, NovelGenerationChain> _chainCache = {};
  final Map<String, DetailedOutlineChain> _detailedChainCache = {};

  // Define batch size for outline generation
  static const int _outlineBatchSize = 10; // Generate 20 chapters per batch

  NovelGenerationService({
    required ApiConfigController apiConfigController,
  }) : _apiConfigController = apiConfigController {
    _embeddingService =
        Get.put(EmbeddingService(apiConfigController: apiConfigController));
  }

  /// 创建或获取已有的生成链
  Future<NovelGenerationChain> _getChain(String novelTitle) async {
    if (_chainCache.containsKey(novelTitle)) {
      return _chainCache[novelTitle]!;
    }
    final modelConfig = _apiConfigController.getCurrentModel();
    final llm = ModelAdapter.createLLMFromConfig(modelConfig);
    final chain = NovelGenerationChain(
      llm: llm,
      novelTitle: novelTitle,
      // Optionally pass a different summarizer LLM if needed
    );
    _chainCache[novelTitle] = chain;
    return chain;
  }

  /// 创建或获取已有的详细大纲生成链
  Future<DetailedOutlineChain> _getDetailedChain(String novelTitle) async {
    if (_detailedChainCache.containsKey(novelTitle)) {
      return _detailedChainCache[novelTitle]!;
    }
    final modelConfig = _apiConfigController.getCurrentModel();
    final llm = ModelAdapter.createLLMFromConfig(modelConfig);
    final chain = DetailedOutlineChain(llm: llm);
    _detailedChainCache[novelTitle] = chain;
    return chain;
  }

  /// Helper to get NovelMemory associated with a novel title
  NovelMemory _getNovelMemory(String novelTitle, {String? sessionId}) {
    // This is a simplified approach. Ideally, NovelGenerationChain would expose its memory.
    return NovelMemory(novelTitle: novelTitle, sessionId: sessionId);
  }

  /// 清除缓存的生成链
  void clearChain(String novelTitle) {
    _chainCache.remove(novelTitle);
    _detailedChainCache.remove(novelTitle);
    // Consider clearing NovelMemory here too if it's tightly coupled
    // await _getNovelMemory(novelTitle).clear(); // Example
  }

  /// 清除所有缓存的生成链
  void clearAllChains() {
    _chainCache.clear();
    _detailedChainCache.clear();
    print('已清除所有缓存的 NovelGenerationChain 和 DetailedOutlineChain 实例');
    // Consider clearing all NovelMemory instances
  }

  /// 获取聊天链，用于生成对话回复
  Future<NovelGenerationChain> getChatChain(String novelTitle) async {
    return _getChain(novelTitle);
  }

  /// Helper to build character details string (Corrected field names)
  String _buildCharacterDetailsString(dynamic cards) {
    // 处理空输入
    if (cards == null) return '无角色设定';

    // 处理 Map<String, CharacterCard> 类型
    if (cards is Map<String, CharacterCard>) {
      if (cards.isEmpty) return '无角色设定';
      return cards.entries.map((entry) {
        final card = entry.value;
        // Use correct field names based on linter errors and common patterns
        return '''
- ${card.name}:
  性别: ${card.gender ?? '未指定'}
  年龄: ${card.age ?? '未指定'}
  性格: ${card.personalityTraits ?? '未指定'}
  外貌: ${card.appearance ?? '未指定'}
  背景: ${card.background ?? '未指定'}
  技能: ${card.abilities ?? '未指定'}
  目标: ${card.motivation ?? '未指定'}
''';
      }).join('\n');
    }

    // 处理 List<CharacterCard> 类型
    if (cards is List<CharacterCard>) {
      if (cards.isEmpty) return '无角色设定';
      return cards.map((card) {
        return '''
- ${card.name}:
  性别: ${card.gender ?? '未指定'}
  年龄: ${card.age ?? '未指定'}
  性格: ${card.personalityTraits ?? '未指定'}
  外貌: ${card.appearance ?? '未指定'}
  背景: ${card.background ?? '未指定'}
  技能: ${card.abilities ?? '未指定'}
  目标: ${card.motivation ?? '未指定'}
''';
      }).join('\n');
    }

    // 处理其他类型
    return '无角色设定';
  }

  /// 生成续写大纲 (专门用于扩展现有小说)
  Future<String> generateOutlineForContinuation({
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required int startChapter,
    required int endChapter,
    required NovelOutline existingOutline,
    String? background,
    String? otherRequirements,
    WritingStylePackage? writingStyle,
    Map<String, CharacterCard>? characterCards,
    void Function(int currentBatch, int totalBatches)? onProgress,
    void Function(String)? onRealtimeOutput,
  }) async {
    if (startChapter <= 0 || endChapter < startChapter) {
      throw ArgumentError('章节范围无效：startChapter=$startChapter, endChapter=$endChapter');
    }

    print(
        "[NovelGenerationService.generateOutlineForContinuation] 开始为 '$novelTitle' 生成续写大纲（第$startChapter-$endChapter章）");

    // 实时输出初始信息
    onRealtimeOutput?.call("开始生成《$novelTitle》的续写大纲\n");
    onRealtimeOutput?.call("续写范围: 第$startChapter章到第$endChapter章\n");
    onRealtimeOutput?.call("现有章节: ${existingOutline.chapters.length}章\n");

    // 计算需要生成的章节数
    int chaptersToGenerate = endChapter - startChapter + 1;
    int numberOfBatches = (chaptersToGenerate / _outlineBatchSize).ceil();

    // 实时输出批次信息
    onRealtimeOutput?.call("需要生成 $chaptersToGenerate 章，计算得出需要 $numberOfBatches 个批次\n\n");

    try {
      final chain = await _getChain(novelTitle);
      final novelMemory = _getNovelMemory(novelTitle);

      // 将现有大纲保存到memory中
      await novelMemory.saveOutline(jsonEncode(existingOutline.toJson()));

      // 构建基础输入
      final baseInput = {
        'novelTitle': novelTitle,
        'genres': genres.join(', '),
        'theme': theme,
        'targetReaders': targetReaders,
        'background': background ?? '',
        'otherRequirements': otherRequirements ?? '',
        'writingStyle': writingStyle?.toJson() ?? {},
        'characterCards': characterCards?.map((key, value) => MapEntry(key, value.toJson())) ?? {},
        'knowledgeBase': '无知识库信息',
        'task': 'generate_continuation_outline',
        'existingChaptersCount': existingOutline.chapters.length.toString(),
      };

      List<Map<String, dynamic>> allChapters = [];

      for (int i = 0; i < numberOfBatches; i++) {
        int batchStartChapter = startChapter + (i * _outlineBatchSize);
        int batchEndChapter = min(batchStartChapter + _outlineBatchSize - 1, endChapter);

        print(
            "[NovelGenerationService.generateOutlineForContinuation] 处理批次 ${i + 1}/$numberOfBatches (章节 $batchStartChapter-$batchEndChapter)...");

        // 实时输出当前批次信息
        onRealtimeOutput?.call(
            "📝 正在处理批次 ${i + 1}/$numberOfBatches (第$batchStartChapter-$batchEndChapter章)...\n");

        final batchInput = {
          ...baseInput,
          'startChapter': batchStartChapter.toString(),
          'endChapter': batchEndChapter.toString(),
          'input': "基于现有大纲，生成《$novelTitle》第$batchStartChapter到第$batchEndChapter章的续写大纲",
        };

        try {
          // 使用流式模式生成
          final buffer = StringBuffer();
          final resultStream = chain.streamText(batchInput);

          await for (final chunk in resultStream) {
            buffer.write(chunk);
          }

          final rawResult = buffer.toString();

          // 解析JSON响应
          try {
            final decodedResult = jsonDecode(rawResult);
            if (decodedResult is List) {
              List<Map<String, dynamic>> batchChapters = [];
              for (var item in decodedResult) {
                if (item is Map<String, dynamic> &&
                    item.containsKey('chapterNumber') &&
                    item['chapterNumber'] is int &&
                    item.containsKey('chapterTitle') &&
                    item['chapterTitle'] is String &&
                    item.containsKey('summary') &&
                    item['summary'] is String &&
                    item['chapterNumber'] >= batchStartChapter &&
                    item['chapterNumber'] <= batchEndChapter) {
                  batchChapters.add(item);
                }
              }

              if (batchChapters.isNotEmpty) {
                allChapters.addAll(batchChapters);
                onRealtimeOutput?.call("✅ 批次 ${i + 1} 完成，生成了 ${batchChapters.length} 章\n");
              } else {
                onRealtimeOutput?.call("⚠️ 批次 ${i + 1} 未生成有效章节\n");
              }
            } else {
              onRealtimeOutput?.call("⚠️ 批次 ${i + 1} 返回格式不正确\n");
            }
          } catch (jsonError) {
            print("[NovelGenerationService.generateOutlineForContinuation] 批次 ${i + 1} JSON解析失败: $jsonError");
            onRealtimeOutput?.call("⚠️ 批次 ${i + 1} JSON解析失败\n");
          }
        } catch (e) {
          print("[NovelGenerationService.generateOutlineForContinuation] 批次 ${i + 1} 生成失败: $e");
          onRealtimeOutput?.call("❌ 批次 ${i + 1} 生成失败: $e\n");
        }

        // 批次间延迟
        if (i < numberOfBatches - 1) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      }

      // 合并现有大纲和新生成的章节
      final combinedChapters = <Map<String, dynamic>>[];

      // 添加现有章节
      for (final chapter in existingOutline.chapters) {
        combinedChapters.add(chapter.toJson());
      }

      // 添加新生成的章节
      combinedChapters.addAll(allChapters);

      final finalOutline = {
        'novelTitle': novelTitle,
        'chapters': combinedChapters,
      };

      final result = jsonEncode(finalOutline);

      onRealtimeOutput?.call("\n🎉 续写大纲生成完成！\n");
      onRealtimeOutput?.call("总章节数: ${combinedChapters.length}章\n");
      onRealtimeOutput?.call("新增章节: ${allChapters.length}章\n");

      return result;
    } catch (e) {
      print("[NovelGenerationService.generateOutlineForContinuation] 生成续写大纲失败: $e");
      onRealtimeOutput?.call("❌ 生成续写大纲失败: $e\n");
      rethrow;
    }
  }

  /// 生成小说大纲 (Batched JSON Version)
  /// Returns the complete outline as a JSON string.
  Future<String> generateOutline({
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required int totalChapters,
    String? background,
    String? otherRequirements,
    WritingStylePackage? writingStyle,
    Map<String, CharacterCard>? characterCards,
    void Function(int currentBatch, int totalBatches)? onProgress,
    void Function(String)? onRealtimeOutput,
  }) async {
    if (totalChapters <= 0) {
      throw ArgumentError('总章节数必须大于 0。');
    }

    print(
        "[NovelGenerationService.generateOutline] 开始为 '$novelTitle' ($totalChapters 章) 生成分批大纲。批次大小: $_outlineBatchSize");

    // 实时输出初始信息
    onRealtimeOutput?.call("开始生成《$novelTitle》的大纲，共$totalChapters章\n");
    onRealtimeOutput?.call("批次大小: $_outlineBatchSize章/批次\n");

    // 获取当前模型配置，确保使用正确的API路径
    final modelConfig = _apiConfigController.getCurrentModel();
    print('[NovelGenerationService.generateOutline] 使用模型: ${modelConfig.name}');
    print(
        '[NovelGenerationService.generateOutline] API格式: ${modelConfig.apiFormat}');
    print(
        '[NovelGenerationService.generateOutline] API路径: ${modelConfig.apiPath}');
    print(
        '[NovelGenerationService.generateOutline] 使用代理: ${modelConfig.useProxy}');

    // 实时输出模型信息
    onRealtimeOutput?.call("使用模型: ${modelConfig.name}\n");

    // 如果是Google API，确保路径正确
    if (modelConfig.apiFormat == 'Google API' &&
        !modelConfig.apiPath.contains(':generateContent')) {
      // 自动修正路径
      final correctPath = '/v1beta/models/${modelConfig.model}:generateContent';
      print(
          '[NovelGenerationService.generateOutline] 自动修正Google API路径为: $correctPath');
      await _apiConfigController.updateModelConfig(
        modelConfig.name,
        apiPath: correctPath,
      );
    }

    final chain = await _getChain(novelTitle);
    final novelMemory =
        _getNovelMemory(novelTitle, sessionId: null); // Get memory instance

    // Prepare base input common to all batches
    String charactersString =
        _buildCharacterDetailsString(characterCards ?? {});
    String writingStylePromptString = writingStyle?.getPrompt() ?? '';
    String knowledgeBaseString = '';
    final knowledgeBaseController = Get.find<KnowledgeBaseController>();
    if (knowledgeBaseController.useKnowledgeBase.value &&
        knowledgeBaseController.selectedDocIds.isNotEmpty) {
      knowledgeBaseString = knowledgeBaseController.getSelectedDocsContent();
    }

    final baseInput = {
      'novelTitle': novelTitle,
      'genres': genres.join(', '),
      'theme': theme ?? '无主题',
      'targetReaders': targetReaders ?? '通用',
      'totalChapters': totalChapters.toString(), // Keep total for context
      'background': background ?? '无背景',
      'otherRequirements': otherRequirements ?? '无其他要求',
      'characters': charactersString,
      'writingStylePrompt': writingStylePromptString.isNotEmpty
          ? writingStylePromptString
          : '无特定文风要求',
      'knowledgeBase':
          knowledgeBaseString.isNotEmpty ? knowledgeBaseString : '无知识库信息',
      'task': 'generate_outline',
    };

    List<Map<String, dynamic>> allChapters = [];
    int numberOfBatches = (totalChapters / _outlineBatchSize).ceil();

    // 实时输出批次信息
    onRealtimeOutput?.call("计算得出需要 $numberOfBatches 个批次\n\n");

    for (int i = 0; i < numberOfBatches; i++) {
      int startChapter = i * _outlineBatchSize + 1;
      int endChapter = min((i + 1) * _outlineBatchSize, totalChapters);
      print(
          "[NovelGenerationService.generateOutline] 处理批次 ${i + 1}/$numberOfBatches (章节 $startChapter-$endChapter)...");

      // 实时输出当前批次信息
      onRealtimeOutput?.call(
          "📝 正在处理批次 ${i + 1}/$numberOfBatches (第$startChapter-$endChapter章)...\n");

      onProgress?.call(i + 1, numberOfBatches);

      // 实时输出上下文信息
      if (startChapter > 1) {
        onRealtimeOutput?.call("📖 使用langchain memory隐性传递前文上下文\n");
      }

      final batchInput = {
        ...baseInput,
        'startChapter': startChapter.toString(),
        'endChapter': endChapter.toString(),
        'currentBatch': (i + 1).toString(),
        'totalBatches': numberOfBatches.toString(),
        'input':
            "生成《${baseInput['novelTitle']}》第$startChapter到第$endChapter章的大纲",
      };

      int retryCount = 0;
      const int maxRetries = 2; // Allow up to 2 retries per batch
      bool batchSuccess = false;

      while (retryCount <= maxRetries && !batchSuccess) {
        try {
          // 使用流式模式而不是run方法，以支持阿里云通义千问模型
          print(
              "[NovelGenerationService.generateOutline] 使用流式模式生成批次 ${i + 1} (尝试 ${retryCount + 1})...");

          // 实时输出重试信息
          if (retryCount > 0) {
            onRealtimeOutput?.call("⚠️ 批次 ${i + 1} 第${retryCount + 1}次尝试...\n");
          } else {
            onRealtimeOutput?.call("🔄 开始生成批次 ${i + 1}...\n");
          }

          final buffer = StringBuffer();
          final resultStream = chain.stream(batchInput);

          await for (final chunk in resultStream) {
            buffer.write(chunk);
          }

          final rawResult = buffer.toString();
          print(
              "[NovelGenerationService.generateOutline] 批次 ${i + 1} (尝试 ${retryCount + 1}) 原始结果长度: ${rawResult.length}");

          // 实时输出生成结果长度
          onRealtimeOutput
              ?.call("📄 批次 ${i + 1} 生成完成，内容长度: ${rawResult.length}\n");

          dynamic decodedResult;
          try {
            String cleanedResult = rawResult.trim();
            // More robust cleaning for potential ```json ... ``` markers
            if (cleanedResult.startsWith("```json")) {
              cleanedResult = cleanedResult.substring(7).trim();
            }
            if (cleanedResult.endsWith("```")) {
              cleanedResult =
                  cleanedResult.substring(0, cleanedResult.length - 3).trim();
            }

            if (!cleanedResult.startsWith('[') ||
                !cleanedResult.endsWith(']')) {
              throw FormatException(
                  "LLM 未返回有效的 JSON 数组 (未找到 '[' 或 ']'). Raw:\n$cleanedResult");
            }

            decodedResult = jsonDecode(cleanedResult);
            // --- Add Diagnostic Logging ---
            print(
                "[DEBUG] Batch ${i + 1} decodedResult Type: ${decodedResult.runtimeType}");
            // print("[DEBUG] Batch ${i + 1} decodedResult Content: $decodedResult"); // Be cautious logging large content
            // --- End Diagnostic Logging ---
          } on FormatException catch (e) {
            print(
                "[NovelGenerationService.generateOutline] 错误: 批次 ${i + 1} (尝试 ${retryCount + 1}) JSON 解码失败. 错误: $e.");
            // Don't rethrow immediately, allow retry
            rethrow;
          }

          if (decodedResult is List) {
            int chaptersInBatch = 0;
            List<Map<String, dynamic>> batchChapters = [];
            for (var item in decodedResult) {
              if (item is Map<String, dynamic> &&
                  item.containsKey('chapterNumber') &&
                  item['chapterNumber'] is int &&
                  item.containsKey('chapterTitle') &&
                  item['chapterTitle'] is String &&
                  item.containsKey('summary') &&
                  item['summary'] is String &&
                  // Additional check: ensure chapter number is within expected range for the batch
                  item['chapterNumber'] >= startChapter &&
                  item['chapterNumber'] <= endChapter) {
                batchChapters.add(item);
                chaptersInBatch++;
              } else {
                print(
                    "[NovelGenerationService.generateOutline] 警告: 批次 ${i + 1} 中发现无效或超出范围的章节结构: $item. 跳过.");
              }
            }
            // Validate if the number of chapters is reasonable for the batch
            if (chaptersInBatch != (endChapter - startChapter + 1)) {
              print(
                  "[NovelGenerationService.generateOutline] 警告: 批次 ${i + 1} 解析出的章节数 ($chaptersInBatch) 与预期 (${endChapter - startChapter + 1}) 不符.");
              // Decide if this constitutes a failure requiring retry
              // For now, accept the parsed chapters but log warning
            }
            allChapters.addAll(batchChapters);
            // --- Add Diagnostic Logging ---
            print(
                "[DEBUG] Batch ${i + 1} added ${batchChapters.length} chapters. Current allChapters length: ${allChapters.length}");
            // Check if nesting occurred IMMEDIATELY after addAll
            if (allChapters.isNotEmpty && allChapters.first is List) {
              print(
                  "[DEBUG] *** WARNING: Nesting detected in allChapters immediately after batch ${i + 1} addAll! ***");
            }
            // --- End Diagnostic Logging ---
            print(
                "[NovelGenerationService.generateOutline] 批次 ${i + 1} (尝试 ${retryCount + 1}) 成功解析. 添加了 $chaptersInBatch 个章节.");

            // 实时输出批次成功信息
            onRealtimeOutput
                ?.call("✅ 批次 ${i + 1} 成功！解析了 $chaptersInBatch 个章节\n");

            // 手动保存当前批次的结果到LangChain memory，确保下一批次能获取到上下文
            try {
              // 构建当前批次的摘要信息，用于传递给下一批次
              final batchSummary = StringBuffer();
              batchSummary
                  .writeln("第${i + 1}批次大纲（第$startChapter-$endChapter章）：");
              for (final chapter in batchChapters) {
                final chapterNum = chapter['chapterNumber'];
                final chapterTitle = chapter['chapterTitle'];
                final summary = chapter['summary'];
                batchSummary.writeln("第$chapterNum章：$chapterTitle");
                batchSummary.writeln("概要：$summary");
                batchSummary.writeln("---");
              }

              // 保存到LangChain memory
              final contextInput = {
                'input': "生成第$startChapter到第$endChapter章的大纲"
              };
              final contextOutput = {'output': batchSummary.toString()};

              await chain.memory.saveContext(
                  inputValues: contextInput, outputValues: contextOutput);

              print(
                  "[NovelGenerationService.generateOutline] 批次 ${i + 1} 上下文已保存到LangChain内存");
              onRealtimeOutput?.call("💾 批次 ${i + 1} 上下文已保存，供后续批次使用\n");
            } catch (e) {
              print(
                  "[NovelGenerationService.generateOutline] 保存批次 ${i + 1} 上下文时出错: $e");
              // 不中断流程，继续执行
            }

            batchSuccess = true; // Mark batch as successful
          } else {
            throw FormatException(
                "解码结果不是 List 类型. 类型: ${decodedResult.runtimeType}");
          }
        } catch (e) {
          print(
              "[NovelGenerationService.generateOutline] 错误: 处理批次 ${i + 1} (尝试 ${retryCount + 1}) 失败: $e");

          // 实时输出错误信息
          onRealtimeOutput?.call("❌ 批次 ${i + 1} 第${retryCount + 1}次尝试失败: $e\n");

          retryCount++;
          if (retryCount > maxRetries) {
            print(
                "[NovelGenerationService.generateOutline] 错误: 批次 ${i + 1} 达到最大重试次数 ($maxRetries). 大纲生成失败.");

            // 实时输出最终失败信息
            onRealtimeOutput?.call("💥 批次 ${i + 1} 达到最大重试次数，生成失败！\n");

            throw Exception("处理大纲批次 ${i + 1} 失败，已达到最大重试次数.");
          } else {
            print(
                "[NovelGenerationService.generateOutline] 在 ${3 * retryCount} 秒后重试批次 ${i + 1}...");

            // 实时输出重试等待信息
            onRealtimeOutput
                ?.call("⏳ 等待 ${3 * retryCount} 秒后重试批次 ${i + 1}...\n");

            await Future.delayed(Duration(
                seconds: 3 * retryCount)); // Exponential backoff (simple)
          }
        }
      } // End retry loop
    } // End batch loop

    // --- Aggregation and Saving ---
    // 实时输出完成信息
    onRealtimeOutput?.call("\n🎉 所有批次处理完成！\n");
    onRealtimeOutput?.call("📊 统计信息：\n");
    onRealtimeOutput?.call("  - 请求章节数：$totalChapters\n");
    onRealtimeOutput?.call("  - 实际生成数：${allChapters.length}\n");

    if (allChapters.length != totalChapters) {
      print(
          "[NovelGenerationService.generateOutline] 警告: 最终章节计数 (${allChapters.length}) 与请求的总数 ($totalChapters) 不符. 可能缺少或解析失败了部分章节.");

      // 实时输出警告信息
      onRealtimeOutput?.call("⚠️ 警告：生成的章节数与请求数不符！\n");
    } else {
      onRealtimeOutput?.call("✅ 章节数量完全匹配！\n");
    }

    allChapters.sort((a, b) =>
        (a['chapterNumber'] as int).compareTo(b['chapterNumber'] as int));

    final finalOutlineJson = {
      'novelTitle': novelTitle,
      'chapters': allChapters,
      'generatedAt': DateTime.now().toIso8601String(),
      'totalChaptersGenerated':
          allChapters.length, // Store the actual count generated
      'totalChaptersRequested': totalChapters,
    };

    String finalJsonString;
    try {
      finalJsonString = jsonEncode(finalOutlineJson);
      print(
          "[NovelGenerationService.generateOutline] 最终聚合的大纲 JSON 已生成 (长度: ${finalJsonString.length}).");
    } catch (e) {
      print("[NovelGenerationService.generateOutline] 错误: 无法编码最终 JSON 大纲: $e");
      throw Exception("无法创建最终的大纲 JSON 结构.");
    }

    // Save the complete JSON outline OBJECT string to NovelMemory
    try {
      await novelMemory.saveOutline(finalJsonString);
      print(
          "[NovelGenerationService.generateOutline] 完整 JSON 大纲已保存到 NovelMemory ('$novelTitle').");
    } catch (e) {
      print(
          "[NovelGenerationService.generateOutline] 错误: 无法将最终 JSON 大纲保存到 NovelMemory: $e");
      throw Exception("无法保存生成的大纲.");
    }

    // 保存大纲生成记录到对话历史
    final userMessage = app_chat.ChatMessage.user(
      content: "生成《$novelTitle》的大纲，共$totalChapters章",
      novelTitle: novelTitle,
    );
    await Get.find<ChatHistoryService>().addMessage(userMessage);

    // 生成简单的大纲摘要作为AI回复
    final chapters = finalOutlineJson['chapters'] as List<dynamic>? ?? [];
    final summaryBuffer =
        StringBuffer("已生成《$novelTitle》的大纲，共${chapters.length}章:\n");
    for (var chapter in chapters) {
      if (chapter is Map<String, dynamic>) {
        final chapterNumber = chapter['chapterNumber'];
        final chapterTitle = chapter['chapterTitle'];
        summaryBuffer.writeln("- 第$chapterNumber章：$chapterTitle");
      }
    }

    final aiMessage = app_chat.ChatMessage.ai(
      content: summaryBuffer.toString(),
      novelTitle: novelTitle,
    );
    await Get.find<ChatHistoryService>().addMessage(aiMessage);

    // Return ONLY the JSON array string of chapters for the UI/Controller
    try {
      final List<dynamic> chaptersList =
          finalOutlineJson['chapters'] as List<dynamic>? ?? [];
      final String chaptersJsonArrayString = jsonEncode(chaptersList);
      print(
          "[NovelGenerationService.generateOutline] 返回纯章节 JSON 数组字符串 (长度: ${chaptersJsonArrayString.length}).");

      // 实时输出最终完成信息
      onRealtimeOutput?.call("\n🎉 大纲生成完全完成！\n");
      onRealtimeOutput?.call("📋 最终结果：\n");
      onRealtimeOutput?.call("  - 成功生成 ${chaptersList.length} 个章节\n");
      onRealtimeOutput
          ?.call("  - JSON数据长度：${chaptersJsonArrayString.length} 字符\n");
      onRealtimeOutput?.call("✅ 大纲已保存到内存和对话历史\n");

      return chaptersJsonArrayString;
    } catch (e) {
      print("[NovelGenerationService.generateOutline] 错误: 无法编码纯章节 JSON 数组: $e");
      onRealtimeOutput?.call("❌ 无法编码章节JSON数组: $e\n");
      // Fallback to returning the full string if encoding chapters fails?
      // Or rethrow? For now, rethrow.
      throw Exception("无法为 UI 准备章节 JSON 数组.");
    }

    // return finalJsonString; // Old: Return the full JSON string
  }

  /// Generates a detailed Markdown outline for a specific chapter using DetailedOutlineChain.
  Future<String> generateDetailedChapterOutline({
    required String novelTitle,
    required int chapterNumber,
    // Required context for the detailed outline prompt
    required List<String> genres,
    required String theme,
    required String targetReaders,
    String? background,
    String? otherRequirements,
    WritingStylePackage? writingStyle,
    Map<String, CharacterCard>? characterCards,
  }) async {
    print(
        "[NovelGenerationService.generateDetailedChapterOutline] 为 '$novelTitle', 第 $chapterNumber 章 生成详细大纲...");

    final detailedChain = await _getDetailedChain(novelTitle);
    final novelMemory = _getNovelMemory(novelTitle, sessionId: null);

    // 1. Retrieve and parse the stored full JSON outline
    Map<String, dynamic> fullOutline;
    try {
      final outlineJsonString = await novelMemory.getOutline();
      if (outlineJsonString == null || outlineJsonString.isEmpty) {
        throw Exception("未找到或为空: '$novelTitle' 的已存储大纲.");
      }
      fullOutline = jsonDecode(outlineJsonString) as Map<String, dynamic>;
    } catch (e) {
      print(
          "[NovelGenerationService.generateDetailedChapterOutline] 错误: 检索/解析已存储大纲失败: $e");
      throw Exception("无法加载或解析基础小说大纲. 请先生成主要大纲.");
    }

    // 2. Find the specific chapter's data
    Map<String, dynamic>? targetChapterData;
    if (fullOutline.containsKey('chapters') &&
        fullOutline['chapters'] is List) {
      final chaptersList = fullOutline['chapters'] as List;
      targetChapterData = chaptersList.firstWhere(
        (ch) =>
            ch is Map<String, dynamic> && ch['chapterNumber'] == chapterNumber,
        orElse: () => null,
      ) as Map<String, dynamic>?;
    }

    if (targetChapterData == null) {
      throw Exception("在 '$novelTitle' 的已存储大纲中未找到第 $chapterNumber 章.");
    }

    final String chapterTitle =
        targetChapterData['chapterTitle'] as String? ?? '无标题章节 $chapterNumber';
    final String chapterSummary =
        targetChapterData['summary'] as String? ?? '无摘要信息.';

    // 3. Prepare input for DetailedOutlineChain
    String charactersString =
        _buildCharacterDetailsString(characterCards ?? {});
    String writingStylePromptString = writingStyle?.getPrompt() ?? '';
    String knowledgeBaseString = '';
    final knowledgeBaseController = Get.find<KnowledgeBaseController>();
    if (knowledgeBaseController.useKnowledgeBase.value &&
        knowledgeBaseController.selectedDocIds.isNotEmpty) {
      knowledgeBaseString = knowledgeBaseController.getSelectedDocsContent();
    }

    final detailedInput = {
      'novelTitle': novelTitle,
      'genres': genres.join(', '),
      'theme': theme ?? '无主题',
      'targetReaders': targetReaders ?? '通用',
      'background': background ?? '无背景',
      'otherRequirements': otherRequirements ?? '无其他要求',
      'characters': charactersString,
      'writingStylePrompt': writingStylePromptString.isNotEmpty
          ? writingStylePromptString
          : '无特定文风要求',
      'knowledgeBase':
          knowledgeBaseString.isNotEmpty ? knowledgeBaseString : '无知识库信息',
      // --- Specific chapter info ---
      'chapterNumber': chapterNumber.toString(), // Pass as string
      'chapterTitle': chapterTitle,
      'chapterSummary': chapterSummary,
    };

    // 保存用户输入到对话历史
    final userMessage = app_chat.ChatMessage.user(
      content: "生成《$novelTitle》第$chapterNumber章的详细大纲",
      novelTitle: novelTitle,
      chapterNumber: chapterNumber,
    );
    await Get.find<ChatHistoryService>().addMessage(userMessage);

    // 4. Call the DetailedOutlineChain
    try {
      print(
          "[NovelGenerationService.generateDetailedChapterOutline] 调用 DetailedOutlineChain 生成第 $chapterNumber 章的细纲...");
      final String detailedOutlineMarkdown =
          await detailedChain.generateDetailedOutlineForChapter(detailedInput);

      print(
          "[NovelGenerationService.generateDetailedChapterOutline] 第 $chapterNumber 章详细大纲生成成功 (长度: ${detailedOutlineMarkdown.length}).");

      // 保存AI回复到对话历史
      final aiMessage = app_chat.ChatMessage.ai(
        content: "已生成《$novelTitle》第$chapterNumber章的详细大纲",
        novelTitle: novelTitle,
        chapterNumber: chapterNumber,
      );
      await Get.find<ChatHistoryService>().addMessage(aiMessage);

      return detailedOutlineMarkdown.trim(); // Return trimmed markdown
    } catch (e) {
      print(
          "[NovelGenerationService.generateDetailedChapterOutline] 调用 DetailedOutlineChain 时出错: $e");
      rethrow;
    }
  }

  /// 生成章节内容
  Future<String> generateChapter({
    required String novelTitle,
    required int chapterNumber,
    required String chapterTitle,
    required String
        outlineContent, // IMPORTANT: Expects DETAILED MARKDOWN outline
    required List<String> genres,
    required String theme,
    required String targetReaders,
    String? background,
    String? otherRequirements,
    String? previousChapterSummary,
    WritingStylePackage? writingStyle,
    Map<String, CharacterCard>? characterCards,
    void Function(String)? onProgress,
    String? sessionId,
  }) async {
    print(
        "[NovelGenerationService.generateChapter] 为 '$novelTitle', 第 $chapterNumber 章 '$chapterTitle' 生成内容. (使用详细大纲)");
    try {
      // 获取当前模型配置，确保使用正确的API路径
      final modelConfig = _apiConfigController.getCurrentModel();
      print(
          '[NovelGenerationService.generateChapter] 使用模型: ${modelConfig.name}');
      print(
          '[NovelGenerationService.generateChapter] API格式: ${modelConfig.apiFormat}');
      print(
          '[NovelGenerationService.generateChapter] API路径: ${modelConfig.apiPath}');
      print(
          '[NovelGenerationService.generateChapter] 使用代理: ${modelConfig.useProxy}');

      // 如果是Google API，确保路径正确
      if (modelConfig.apiFormat == 'Google API' &&
          !modelConfig.apiPath.contains(':generateContent')) {
        // 自动修正路径
        final correctPath =
            '/v1beta/models/${modelConfig.model}:generateContent';
        print(
            '[NovelGenerationService.generateChapter] 自动修正Google API路径为: $correctPath');
        await _apiConfigController.updateModelConfig(
          modelConfig.name,
          apiPath: correctPath,
        );
      }

      final chain = await _getChain(novelTitle);
      final novelMemory = sessionId != null
          ? NovelMemory(novelTitle: novelTitle, sessionId: sessionId)
          : _getNovelMemory(novelTitle);

      // 获取全部历史内容作为上下文
      String previousChaptersContext = '';
      String fullNovelContext = '';

      try {
        print("[NovelGenerationService.generateChapter] 获取完整的小说上下文...");

        // 获取之前章节的内容
        if (chapterNumber > 1) {
          // 检查是否启用了嵌入模型
          if (_apiConfigController.embeddingModel.value.enabled) {
            try {
              print(
                  "[NovelGenerationService.generateChapter] 使用嵌入模型获取相关章节内容...");

              // 获取当前章节的大纲和细纲
              final chapterOutline = outlineContent;

              // 获取所有已生成的章节
              final allPreviousChapters = await novelMemory.getAllChapters();

              // 将章节转换为文档格式
              final List<Map<String, dynamic>> documents = [];
              allPreviousChapters.forEach((number, content) {
                if (number < chapterNumber) {
                  documents.add({
                    'chapterNumber': number,
                    'text': content,
                  });
                }
              });

              if (documents.isNotEmpty) {
                // 使用嵌入模型查找与当前章节相关的内容
                final similarDocuments = await _embeddingService
                    .findSimilarTexts(chapterOutline, documents,
                        textField: 'text');

                // 构建上下文
                final buffer = StringBuffer();
                buffer.writeln("# 相关章节内容");

                for (final doc in similarDocuments) {
                  final chapterNum = doc['chapterNumber'] as int;
                  final similarity = doc['similarity'] as double;
                  final text = doc['text'] as String;

                  buffer.writeln(
                      "## 第$chapterNum章 (相似度: ${similarity.toStringAsFixed(2)})");
                  // 只取前2000个字符，避免上下文过长
                  buffer.writeln(text.length > 2000
                      ? "${text.substring(0, 2000)}..."
                      : text);
                  buffer.writeln();
                }

                previousChaptersContext = buffer.toString();
                print(
                    "[NovelGenerationService.generateChapter] 成功使用嵌入模型获取相关章节，长度: ${previousChaptersContext.length}");
              } else {
                print("[NovelGenerationService.generateChapter] 没有找到已生成的章节");
              }
            } catch (e) {
              print("[NovelGenerationService.generateChapter] 使用嵌入模型时出错: $e");
              // 如果嵌入模型出错，回退到传统方式
              previousChaptersContext =
                  await novelMemory.getPreviousChapters(chapterNumber);
            }
          } else {
            // 传统方式获取之前章节
            previousChaptersContext =
                await novelMemory.getPreviousChapters(chapterNumber);
          }

          print(
              "[NovelGenerationService.generateChapter] 成功获取之前章节内容，长度: ${previousChaptersContext.length}");

          // 如果没有提供上一章摘要，则自动生成
          if (previousChapterSummary == null ||
              previousChapterSummary.isEmpty) {
            final previousChapterContent =
                await novelMemory.getChapter(chapterNumber - 1);
            if (previousChapterContent != null &&
                previousChapterContent.isNotEmpty) {
              previousChapterSummary =
                  "上一章中，${previousChapterContent.substring(0, min(300, previousChapterContent.length))}...";
              print(
                  "[NovelGenerationService.generateChapter] 自动生成了上一章摘要，长度: ${previousChapterSummary.length}");
            }
          }
        }

        // 获取大纲信息
        final outlineJson = await novelMemory.getOutline();
        if (outlineJson != null && outlineJson.isNotEmpty) {
          print(
              "[NovelGenerationService.generateChapter] 成功获取小说大纲，长度: ${outlineJson.length}");

          // 将大纲信息添加到上下文中
          fullNovelContext =
              "# 小说大纲\n$outlineJson\n\n---\n\n$previousChaptersContext";
        } else {
          fullNovelContext = previousChaptersContext;
        }

        print(
            "[NovelGenerationService.generateChapter] 已准备完整的小说上下文，总长度: ${fullNovelContext.length}");
        // 使用完整上下文替换之前的章节上下文
        previousChaptersContext = fullNovelContext;
      } catch (e) {
        print("[NovelGenerationService.generateChapter] 获取小说上下文时出错: $e");
        // 出错时不中断执行，使用空字符串
        previousChaptersContext = '';
      }

      // Prepare input for chapter generation
      String knowledgeBaseString = '';
      final knowledgeBaseController = Get.find<KnowledgeBaseController>();
      if (knowledgeBaseController.useKnowledgeBase.value &&
          knowledgeBaseController.selectedDocIds.isNotEmpty) {
        knowledgeBaseString = knowledgeBaseController.getSelectedDocsContent();
      }
      String charactersString =
          _buildCharacterDetailsString(characterCards ?? {});

      // 准备章节生成的输入参数
      final input = {
        'novelTitle': novelTitle,
        'genres': genres.join(', '),
        'chapterNumber': chapterNumber.toString(),
        'chapterTitle': chapterTitle,
        'outlineContent': outlineContent, // Pass the detailed markdown outline
        'task': 'generate_chapter',
        'theme': theme ?? '无主题',
        'targetReaders': targetReaders ?? '通用',
        'background': background ?? '无背景',
        'otherRequirements': otherRequirements ?? '无其他要求',
        'previousChapterSummary': previousChapterSummary ?? '无上一章摘要',
        'knowledgeBase':
            knowledgeBaseString.isNotEmpty ? knowledgeBaseString : '无知识库信息',
        'characters': charactersString,
        'writingStylePrompt': writingStyle?.getPrompt() ?? '无特定文风要求',
        // 添加完整的小说上下文作为输入
        'input': "生成第$chapterNumber章：$chapterTitle",
        'history': previousChaptersContext,
      };

      // 打印历史上下文的长度
      print(
          "[NovelGenerationService.generateChapter] 历史上下文长度: ${previousChaptersContext.length}");

      // 确保历史上下文不为空
      if (previousChaptersContext.isEmpty) {
        print("[NovelGenerationService.generateChapter] 警告: 历史上下文为空");
      }

      // --- Detailed Input Logging ---
      print(
          '[NovelGenerationService.generateChapter] --- Input to Chain --- START ---');
      input.forEach((key, value) {
        final valueStr = value.toString();
        final displayValue = valueStr.length > 200
            ? '${valueStr.substring(0, 200)}...'
            : valueStr; // Truncate long values
        print('  [$key]: $displayValue');
      });
      print(
          '[NovelGenerationService.generateChapter] --- Input to Chain --- END ---');
      // --- End Detailed Input Logging ---

      // 保存用户输入到对话历史
      final userMessage = app_chat.ChatMessage.user(
        content: "生成第$chapterNumber章：$chapterTitle",
        novelTitle: novelTitle,
        chapterNumber: chapterNumber,
      );
      await Get.find<ChatHistoryService>().addMessage(userMessage);

      String result;

      // Execute chain (stream or run)
      if (onProgress != null) {
        final resultStream = chain.stream(input);
        final buffer = StringBuffer();
        await for (final chunk in resultStream) {
          buffer.write(chunk);
          onProgress(chunk);
        }
        print("[NovelGenerationService.generateChapter] 章节生成流结束.");
        result = buffer.toString();
      } else {
        print("[NovelGenerationService.generateChapter] 运行章节生成 (非流式)...");
        result = await chain.run(input);
        print(
            "[NovelGenerationService.generateChapter] 章节生成完成 (非流式). 结果长度: ${result.length}");
      }

      // 保存AI回复到对话历史
      final aiMessage = app_chat.ChatMessage.ai(
        content: "已生成第$chapterNumber章：$chapterTitle",
        novelTitle: novelTitle,
        chapterNumber: chapterNumber,
      );
      await Get.find<ChatHistoryService>().addMessage(aiMessage);

      return result;
    } catch (e) {
      print(
          '[NovelGenerationService.generateChapter] 为第 $chapterNumber 章生成章节内容失败: $e');
      rethrow;
    }
  }

  // 续写小说方法已移除，将在后续重新实现
}
